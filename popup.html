<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Reading Friendly</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <h1>YouTube Reading Friendly</h1>

    <div class="toggle-container">
      <label class="toggle-label">
        <span>Enable Reading Mode</span>
        <input type="checkbox" id="reading-mode-toggle">
        <span class="toggle-slider"></span>
      </label>
    </div>

    <div class="toggle-container">
      <label class="toggle-label">
        <span>Enable Focus Mode</span>
        <input type="checkbox" id="focus-mode-toggle">
        <span class="toggle-slider"></span>
      </label>
    </div>

    <div class="settings">
      <h2>Settings</h2>

      <div class="setting-item">
        <label for="font-size">Content Font Size:</label>
        <select id="font-size">
          <option value="small">Small</option>
          <option value="medium" selected>Medium</option>
          <option value="large">Large</option>
        </select>
      </div>

      <div class="setting-item">
        <label for="html-font-size">Text Scaling:</label>
        <select id="html-font-size">
          <option value="default" selected>Default (1×)</option>
          <option value="small">Small (0.8×)</option>
          <option value="medium">Medium (1.2×)</option>
          <option value="large">Large (1.6×)</option>
        </select>
      </div>

      <div class="setting-note">
        <p>Line height is automatically calculated based on content font size</p>
      </div>

      <div class="setting-note">
        <p>Text scaling changes the base font size at HTML level</p>
      </div>
    </div>

    <div class="buttons">
      <button id="reset-btn">Reset Defaults</button>
      <a href="options.html" target="_blank" class="advanced-link">Advanced Options</a>
    </div>

    <div class="footer">
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>
