// CSS template literal tag for syntax highlighting
const css = (strings, ...values) => {
  const result = strings.reduce((acc, str, i) => {
    return acc + str + (values[i] || '');
  }, '');
  return result;
};

// Global variables
let isReadingModeEnabled = false;
let currentSettings = {
  fontSize: 'medium',
  fontFamily: 'youtube-sans',
  customFont: '',
  htmlFontSize: 'default',
  fontWeightTitle: 'bold',
  fontWeightDescription: 'normal',
  fontWeightComments: 'normal',
  boldFontWeight: '700', // New setting to control bold font weight (700 or 500)
  maxWidth: 960,
  hideSidebar: 'always',
  hideComments: false,
  hideEndscreen: true,
  hideMiniGuide: true,
  guideStyle: 'overlay', // 'overlay' or 'push' - controls how the guide opens
  focusMode: false,
  focusOpacity: 70,
  focusModeShortcut: 'Alt+F',
  focusModeNotification: true,
  toggleShortcut: 'Alt+R',
  readingModeNotification: true
};

// Content font size values in rem
const fontSizes = {
  small: {
    title: '1.5rem',
    description: '1rem',
    comments: '1rem'
  },
  medium: {
    title: '2rem',
    description: '1.4rem',
    comments: '1.4rem'
  },
  large: {
    title: '2.75rem',
    description: '2rem',
    comments: '2rem'
  }
};

// Line height multipliers for paragraph spacing
const lineHeightMultipliers = {
  tight: 0.4,    // Tighter spacing
  normal: 0.8,   // Default spacing (current behavior)
  relaxed: 1.2   // More relaxed spacing
};

// Font family definitions
const fontFamilies = {
  system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
  serif: 'Georgia, "Times New Roman", Times, serif',
  'sans-serif': 'Arial, Helvetica, sans-serif',
  monospace: '"Courier New", Courier, monospace',
  'youtube-sans': '"YouTube Sans","Roboto",sans-serif',
  custom: '' // Will be replaced with user's custom font
};

// Font weight values
const fontWeights = {
  thin: '100',
  normal: '400',
  bold: '700' // Default value, will be updated based on user preference
};

// Text scaling values in px (based on YouTube's default 10px)
const htmlFontSizes = {
  default: '10px', // 1× scaling
  small: '8px',    // 0.8× scaling
  medium: '12px',  // 1.2× scaling
  large: '16px'    // 1.6× scaling
};

let isTriggeredFromClick = false;
let preservedGuideState = null; // Store guide state before page changes

// Function to get current guide state
function getCurrentGuideState() {
  const guideElement = document.querySelector('tp-yt-app-drawer');
  if (!guideElement) return null;
  return guideElement.hasAttribute('opened') ? 'opened' : 'closed';
}

// Convert hideSidebar setting for backward compatibility
// Note: hideSidebar now hides entire sidebar only when it contains only related videos
function convertHideSidebarSetting(setting) {
  if (typeof setting === 'boolean') {
    return setting ? 'always' : 'never';
  }
  return setting || 'always';
}

// Initialize extension
function init() {
  // Load saved settings
  chrome.storage.sync.get({
    readingMode: false,
    fontSize: 'medium',
    fontFamily: 'youtube-sans',
    customFont: '',
    htmlFontSize: 'default',
    fontWeightTitle: 'bold',
    fontWeightDescription: 'normal',
    fontWeightComments: 'normal',
    boldFontWeight: '700', // Default to 700 for bold font weight
    maxWidth: 960,
    lineHeight: 'normal', // Default paragraph spacing
    hideSidebar: 'always',
    autoOpenChat: false,
    hideComments: false, // Default from options.js
    hideEndscreen: true,  // Default from options.js
    hideMiniGuide: true,  // Default from options.js
    guideStyle: 'overlay', // Default guide style (overlay or push)
    focusMode: false,
    focusOpacity: 70,
    focusModeShortcut: 'Alt+F',
    focusModeNotification: true,
    toggleShortcut: 'Alt+R',
    readingModeNotification: true
  }, function(settings) {
    isReadingModeEnabled = settings.readingMode;
    currentSettings = {
      fontSize: settings.fontSize,
      fontFamily: settings.fontFamily ?? 'youtube-sans',
      customFont: settings.customFont,
      htmlFontSize: settings.htmlFontSize,
      fontWeightTitle: settings.fontWeightTitle,
      fontWeightDescription: settings.fontWeightDescription,
      fontWeightComments: settings.fontWeightComments,
      boldFontWeight: settings.boldFontWeight,
      maxWidth: settings.maxWidth,
      lineHeight: settings.lineHeight,
      hideSidebar: convertHideSidebarSetting(settings.hideSidebar),
      autoOpenChat: settings.autoOpenChat,
      hideComments: settings.hideComments,
      hideEndscreen: settings.hideEndscreen,
      hideMiniGuide: settings.hideMiniGuide,
      guideStyle: settings.guideStyle || 'overlay',
      focusMode: settings.focusMode,
      focusOpacity: settings.focusOpacity,
      focusModeShortcut: settings.focusModeShortcut || 'Alt+F',
      focusModeNotification: settings.focusModeNotification !== undefined ? settings.focusModeNotification : true,
      toggleShortcut: settings.toggleShortcut || 'Alt+R',
      readingModeNotification: settings.readingModeNotification !== undefined ? settings.readingModeNotification : true
    };

    if (!currentSettings.fontFamily) {
      currentSettings.fontFamily = 'youtube-sans';
    }

    // If custom font is set, update the fontFamilies.custom value
    if (currentSettings.fontFamily === 'custom' && currentSettings.customFont) {
      fontFamilies.custom = currentSettings.customFont;
    }

    // Update the bold font weight based on user preference
    fontWeights.bold = currentSettings.boldFontWeight;

    // Apply featured page styles regardless of reading mode
    applyFeaturedPageStyles();

    // Apply margin to contents without headers
    applyMarginToContentsWithoutHeader();

    // Apply reading mode if enabled
    if (isReadingModeEnabled) {
      applyReadingMode();
    }

    // Apply focus mode if enabled (independent of reading mode)
    applyFocusMode();
    disableDefaultGuideStyle("init");
    // Add listener to guide button
    addGuideButtonListener();

    // Add observer for guide state changes
    observeGuideState();

    // Add keyboard shortcut listeners
    addKeyboardShortcuts();
  });

  // Listen for messages from popup
  chrome.runtime.onMessage.addListener(function(message, _sender, sendResponse) {
    if (message.action === 'toggleReadingMode') {
      isReadingModeEnabled = message.enabled;

      if (isReadingModeEnabled) {
        applyReadingMode();
      } else {
        removeReadingMode();
        // Note: applyGuideStyle is already called in removeReadingMode
        // Ensure focus mode is still applied even when reading mode is disabled
        applyFocusMode();
      }
    } else if (message.action === 'toggleFocusMode') {
      currentSettings.focusMode = message.enabled;

      // Save the focus mode setting
      chrome.storage.sync.set({ focusMode: currentSettings.focusMode });

      // Apply or remove focus mode
      applyFocusMode();
    } else if (message.action === 'updateSettings') {
      currentSettings = {...currentSettings, ...message.settings};

      // Update the bold font weight if it was changed
      if (message.settings.boldFontWeight) {
        fontWeights.bold = message.settings.boldFontWeight;
      }

      if (isReadingModeEnabled) {
        applyReadingMode();
      }

      // Apply focus mode regardless of reading mode state
      applyFocusMode();

      // Check if auto-open chat setting was enabled and sidebar is hidden
      if (message.settings.autoOpenChat && currentSettings.hideSidebar) {
        const strategy = getHidingStrategy();
        if (strategy.hideSidebar) {
          setTimeout(() => {
            autoOpenLiveChat();
          }, 1000);
        }
      }
    }
    // Send response to confirm receipt
    sendResponse({success: true});
    return true;
  });

  // Handle YouTube's dynamic page loading
  observePageChanges();
}

// Add keyboard shortcut listeners
function addKeyboardShortcuts() {
  document.addEventListener('keydown', function(event) {
    // Don't trigger shortcuts when typing in input fields
    if (event.target.tagName === 'INPUT' ||
        event.target.tagName === 'TEXTAREA' ||
        event.target.contentEditable === 'true') {
      return;
    }

    // Parse the reading mode toggle shortcut
    const toggleShortcut = currentSettings.toggleShortcut || 'Alt+R';
    const toggleKeys = toggleShortcut.split('+');

    // Check if the reading mode toggle shortcut matches
    if (matchesShortcut(event, toggleKeys)) {
      event.preventDefault();
      toggleReadingMode();
      return;
    }

    // Parse the focus mode shortcut
    const focusShortcut = currentSettings.focusModeShortcut || 'Alt+F';
    const focusKeys = focusShortcut.split('+');

    // Check if the focus mode shortcut matches
    if (matchesShortcut(event, focusKeys)) {
      event.preventDefault();
      toggleFocusMode();
    }
  });
}

// Helper function to check if event matches shortcut
function matchesShortcut(event, shortcutKeys) {
  const modifiers = {
    'Ctrl': event.ctrlKey,
    'Alt': event.altKey,
    'Shift': event.shiftKey,
    'Meta': event.metaKey
  };

  // Check if all required modifiers are pressed
  for (const key of shortcutKeys) {
    if (key in modifiers) {
      if (!modifiers[key]) return false;
    } else {
      // This is the main key
      if (event.key.toLowerCase() !== key.toLowerCase()) return false;
    }
  }

  // Check that no extra modifiers are pressed
  const requiredModifiers = shortcutKeys.filter(key => key in modifiers);
  const pressedModifiers = Object.keys(modifiers).filter(key => modifiers[key]);

  return requiredModifiers.length === pressedModifiers.length;
}

// Toggle reading mode on/off
function toggleReadingMode() {
  // Toggle the reading mode setting
  isReadingModeEnabled = !isReadingModeEnabled;

  // Save the new setting
  chrome.storage.sync.set({ readingMode: isReadingModeEnabled });

  // Apply or remove reading mode
  if (isReadingModeEnabled) {
    applyReadingMode();
  } else {
    removeReadingMode();
    // Ensure focus mode is still applied even when reading mode is disabled
    applyFocusMode();
  }

  // Show a brief notification if enabled
  if (currentSettings.readingModeNotification) {
    showReadingModeToggleNotification();
  }
}

// Toggle focus mode on/off
function toggleFocusMode() {
  // Toggle the focus mode setting
  currentSettings.focusMode = !currentSettings.focusMode;

  // Save the new setting
  chrome.storage.sync.set({ focusMode: currentSettings.focusMode });

  // Apply or remove focus mode
  applyFocusMode();

  // Show a brief notification if enabled
  if (currentSettings.focusModeNotification) {
    showFocusModeToggleNotification();
  }
}

// Show reading mode toggle notification
function showReadingModeToggleNotification() {
  // Remove existing notification
  const existingNotification = document.getElementById('yt-reading-toggle-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  const notification = document.createElement('div');
  notification.id = 'yt-reading-toggle-notification';
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-family: 'YouTube Sans', 'Roboto', sans-serif;
      z-index: 2147483647;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-align: center;
      min-width: 200px;
    ">
      ${isReadingModeEnabled ? '📖 Reading Mode ON' : '📖 Reading Mode OFF'}
    </div>
  `;
  document.body.appendChild(notification);

  // Fade in
  setTimeout(() => {
    notification.firstElementChild.style.opacity = '1';
  }, 10);

  // Auto-hide after 1.5 seconds
  setTimeout(() => {
    if (notification && notification.parentNode) {
      notification.firstElementChild.style.opacity = '0';
      setTimeout(() => {
        if (notification && notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }
  }, 1500);
}

// Show focus mode toggle notification
function showFocusModeToggleNotification() {
  // Remove existing notification
  const existingNotification = document.getElementById('yt-focus-toggle-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  const notification = document.createElement('div');
  notification.id = 'yt-focus-toggle-notification';
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-family: 'YouTube Sans', 'Roboto', sans-serif;
      z-index: 2147483647;
      opacity: 0;
      transition: opacity 0.3s ease;
      text-align: center;
      min-width: 200px;
    ">
      ${currentSettings.focusMode ? '🔦 Focus Mode ON' : '💡 Focus Mode OFF'}
    </div>
  `;
  document.body.appendChild(notification);

  // Fade in
  setTimeout(() => {
    notification.firstElementChild.style.opacity = '1';
  }, 10);

  // Auto-hide after 1.5 seconds
  setTimeout(() => {
    if (notification && notification.parentNode) {
      notification.firstElementChild.style.opacity = '0';
      setTimeout(() => {
        if (notification && notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }
  }, 1500);
}

// Generate font styles for title, description, and comments
function generateFontStyles() {
  // Get content font size values
  const fontSize = fontSizes[currentSettings.fontSize];

  // Get line height multiplier based on paragraph spacing setting
  const lineHeightMultiplier = lineHeightMultipliers[currentSettings.lineHeight] || lineHeightMultipliers.normal;

  // Calculate line heights by adding the multiplier to font sizes
  // First, extract numeric values from font sizes (remove 'rem')
  const titleFontSize = parseFloat(fontSize.title);
  const descriptionFontSize = parseFloat(fontSize.description);
  const commentsFontSize = parseFloat(fontSize.comments);

  // Get font family
  let fontFamily = fontFamilies[currentSettings.fontFamily];
  if (currentSettings.fontFamily === 'custom' && currentSettings.customFont) {
    fontFamily = currentSettings.customFont;
  }

  return css`
    /* Video title */
    h1.ytd-watch-metadata {
      font-family: ${fontFamily} !important;
      font-size: ${fontSize.title} !important;
      line-height: ${(titleFontSize + lineHeightMultiplier).toFixed(1)}rem !important;
      margin-bottom: 1rem !important;
      max-height: ${((titleFontSize + lineHeightMultiplier)*2).toFixed(1)}rem !important;
    }

    /* Video description */
    #description-inline-expander {
      font-family: ${fontFamily} !important;
      font-size: ${fontSize.description} !important;
      line-height: ${(descriptionFontSize + lineHeightMultiplier).toFixed(1)}rem !important;
    }

    /* Comments */
    ytd-comment-renderer #content-text {
      font-family: ${fontFamily} !important;
      font-size: ${fontSize.comments} !important;
      line-height: ${(commentsFontSize + lineHeightMultiplier).toFixed(1)}rem !important;
    }

    /* Comment author names */
    ytd-comment-renderer #author-text {
      font-family: ${fontFamily} !important;
    }

    /* All YouTube text */
    yt-formatted-string, span, a, p, h1, h2, h3, h4, h5, h6 {
      font-family: ${fontFamily} !important;
    }

    #content-text.ytd-comment-view-model {
      font-family: ${fontFamily} !important;
      font-size: ${fontSize.comments} !important;
      line-height: ${(commentsFontSize + lineHeightMultiplier).toFixed(1)}rem !important;
    }

  `;
}

// Generate YouTube font weight override styles
function generateFontWeightOverrides() {
  return css`
    /* Specific element font weights */
    h1.ytd-watch-metadata {
      font-weight: ${fontWeights[currentSettings.fontWeightTitle]} !important;
      letter-spacing: ${currentSettings.boldFontWeight === '700' && currentSettings.fontWeightTitle === 'bold' ? '0.01em' : '-0.01em'} !important;
    }

    #description-inline-expander {
      font-weight: ${fontWeights[currentSettings.fontWeightDescription]} !important;
      ${currentSettings.boldFontWeight === '700' && currentSettings.fontWeightDescription === 'bold' ? 'letter-spacing: 0.01em !important;' : ''}
    }

    ytd-comment-renderer #content-text,
    #content-text.ytd-comment-view-model {
      font-weight: ${fontWeights[currentSettings.fontWeightComments]} !important;
      ${currentSettings.boldFontWeight === '700' && currentSettings.fontWeightComments === 'bold' ? 'letter-spacing: 0.01em !important;' : ''}
    }

    .bold {
      font-weight: ${currentSettings.boldFontWeight} !important;
      ${currentSettings.boldFontWeight === '700' ? 'letter-spacing: 0.01em !important;' : ''}
    }

    /* Override YouTube's default font weights from 500 to custom bold weight */
    :root {
      --ytd-channel-name-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-video-primary-info-renderer-title-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-menu-renderer-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-watch-metadata-title-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-comment-renderer-author-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-button-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-tab-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-guide-entry-renderer-badge-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-badge-supported-renderer-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-owner-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-subscription-notification-toggle-button-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-thumbnail-overlay-time-status-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-thumbnail-overlay-toggle-button-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-sentiment-bar-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-sentiment-like-tooltip-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-sentiment-dislike-tooltip-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-header-renderer-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-message-input-renderer-count-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-text-message-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-paid-message-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-paid-sticker-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-membership-item-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-ticker-paid-message-item-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-ticker-sponsor-item-renderer-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-header-text-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-author-name-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-button-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-label-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-value-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-unit-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-units-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-units-label-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-live-chat-banner-renderer-countdown-units-value-font-weight: ${currentSettings.boldFontWeight} !important;
      --yt-formatted-string-bold-font-weight: ${currentSettings.boldFontWeight} !important;
      --ytd-grid-max-width: ${currentSettings.maxWidth}px !important;
    }

    /* Direct override for any element with font-weight: 500 */
    *[style*="font-weight: 500"],
    *[style*="font-weight:500"] {
      font-weight: ${currentSettings.boldFontWeight} !important;
      ${currentSettings.boldFontWeight === '700' ? 'letter-spacing: 0.01em !important;' : ''}
    }

    /* Target common YouTube elements that might use font-weight: 500 directly */
    .ytd-channel-name,
    #channel-name,
    .ytd-video-meta-block,
    .ytd-video-primary-info-renderer,
    #author-text,
    ytd-guide-entry-renderer,
    ytd-compact-video-renderer,
    ytd-video-renderer,
    ytd-grid-video-renderer,
    ytd-rich-grid-video-renderer,
    ytd-rich-grid-media,
    ytd-compact-radio-renderer,
    ytd-compact-playlist-renderer,
    ytd-playlist-renderer,
    ytd-grid-playlist-renderer,
    ytd-grid-show-renderer,
    ytd-channel-renderer,
    ytd-backstage-post-renderer,
    ytd-backstage-post-thread-renderer,
    ytd-notification-renderer,
    ytd-app-promo-renderer,
    ytd-shelf-renderer,
    ytd-horizontal-card-list-renderer,
    ytd-rich-section-renderer,
    ytd-rich-shelf-renderer,
    ytd-rich-item-renderer,
    ytd-search-refinement-card-renderer,
    ytd-search-filter-group-renderer,
    ytd-search-filter-renderer,
    ytd-search-sub-menu-renderer,
    ytd-toggle-button-renderer,
    ytd-button-renderer,
    ytd-menu-renderer,
    ytd-menu-service-item-renderer,
    ytd-menu-navigation-item-renderer,
    ytd-subscription-notification-toggle-button-renderer,
    ytd-thumbnail-overlay-time-status-renderer,
    ytd-thumbnail-overlay-toggle-button-renderer,
    ytd-thumbnail-overlay-now-playing-renderer,
    ytd-thumbnail-overlay-resume-playback-renderer,
    ytd-thumbnail-overlay-loading-preview-renderer,
    ytd-thumbnail-overlay-inline-unplayable-renderer,
    ytd-thumbnail-overlay-hover-text-renderer,
    ytd-thumbnail-overlay-end-screen-renderer,
    ytd-thumbnail-overlay-bottom-panel-renderer,
    ytd-badge-supported-renderer,
    ytd-sentiment-bar-renderer,
    ytd-sentiment-like-tooltip-renderer,
    ytd-sentiment-dislike-tooltip-renderer,
    ytd-live-chat-header-renderer,
    ytd-live-chat-message-input-renderer,
    ytd-live-chat-text-message-renderer,
    ytd-live-chat-paid-message-renderer,
    ytd-live-chat-paid-sticker-renderer,
    ytd-live-chat-membership-item-renderer,
    ytd-live-chat-ticker-paid-message-item-renderer,
    ytd-live-chat-ticker-sponsor-item-renderer,
    ytd-live-chat-banner-renderer {
      font-weight: ${currentSettings.boldFontWeight} !important;
      ${currentSettings.boldFontWeight === '700' ? 'letter-spacing: 0.01em !important;' : ''}
    }
  `;
}

// Function to detect if there's a chat panel present
function hasChatPanel() {
  // Check for live chat elements
  const liveChatFrame = document.querySelector('ytd-live-chat-frame');
  const liveChatRenderer = document.querySelector('ytd-live-chat-renderer');
  const chatFrame = document.querySelector('#chatframe');
  const chatContainer = document.querySelector('ytd-watch-flexy[theater] #chat-container');

  return !!(liveChatFrame || liveChatRenderer || chatFrame || chatContainer);
}

// Function to check if live chat is available for the current video
function isLiveChatAvailable() {
  // Check for live chat toggle button or live chat elements that indicate chat is available
  const chatToggleButton = document.querySelector('[aria-label*="chat" i], [aria-label*="Show chat" i], [aria-label*="Hide chat" i]');
  const liveBadge = document.querySelector('.ytp-live-badge, .badge-style-type-live-now');
  const chatReplayButton = document.querySelector('[aria-label*="chat replay" i]');

  // Check if this is a live stream or has chat replay
  return !!(chatToggleButton || liveBadge || chatReplayButton);
}

// Function to auto-open live chat when sidebar is hidden
function autoOpenLiveChat() {
  if (!currentSettings.autoOpenChat) {
    return;
  }

  // Only proceed if we're on a video page
  if (!window.location.pathname.includes('/watch')) {
    return;
  }

  // Check if chat is already visible
  if (hasChatPanel()) {
    console.log('Chat panel already visible, skipping auto-open');
    return;
  }

  // Check if live chat is available for this video
  if (!isLiveChatAvailable()) {
    console.log('Live chat not available for this video');
    return;
  }

  // Try to find and click the chat toggle button
  const chatToggleSelectors = [
    '[aria-label*="Show chat" i]',
    '[aria-label*="chat" i]:not([aria-label*="Hide chat" i])',
    'button[aria-label*="Show chat" i]',
    'yt-button-renderer[aria-label*="Show chat" i]',
    '#chat-toggle-button',
    '.ytp-chrome-controls [aria-label*="chat" i]'
  ];

  for (const selector of chatToggleSelectors) {
    const button = document.querySelector(selector);
    if (button && button.offsetParent !== null) { // Check if button is visible
      console.log('Auto-opening live chat using selector:', selector);
      button.click();
      return;
    }
  }

  // If no toggle button found, try to show chat by manipulating the page layout
  // This is a fallback approach for cases where the toggle button isn't easily found
  setTimeout(() => {
    const chatContainer = document.querySelector('#chat-container, ytd-live-chat-frame');
    if (chatContainer && chatContainer.style.display === 'none') {
      chatContainer.style.display = '';
      console.log('Auto-opened live chat by showing chat container');
    }
  }, 500);
}

// Function to detect if sidebar contains only related videos
function sidebarContainsOnlyRelatedVideos() {
  const sidebar = document.querySelector('#secondary');
  if (!sidebar) return false;

  // Helper function to check if element exists and is not hidden
  const isElementVisible = (selector) => {
    const elements = sidebar.querySelectorAll(selector);
    return Array.from(elements).some(el => {
      // Check if element is hidden via various methods
      if (el.hasAttribute('hidden')) return false;
      if (el.style.display === 'none') return false;

      // Check computed styles for more thorough visibility check
      const computedStyle = window.getComputedStyle(el);
      if (computedStyle.display === 'none') return false;
      if (computedStyle.visibility === 'hidden') return false;
      if (computedStyle.opacity === '0') return false;

      return true;
    });
  };

  // Check for non-related content specifically within the sidebar
  // Note: Only checking for elements that actually appear in the #secondary sidebar
  const hasChat = isElementVisible('ytd-live-chat-frame, ytd-live-chat-renderer, #chatframe');
  const hasPlaylist = isElementVisible('ytd-playlist-panel-renderer, ytd-playlist-sidebar-renderer');
  const hasMerchShelf = isElementVisible('ytd-merch-shelf-renderer');
  const hasDonationShelf = isElementVisible('ytd-donation-shelf-renderer');
  const hasVideoOwner = isElementVisible('ytd-video-owner-renderer');

  // print all off has valuable to log
  // console.log('Sidebar content check - hasChat:', hasChat, 'hasPlaylist:', hasPlaylist, 'hasMerchShelf:', hasMerchShelf, 'hasDonationShelf:', hasDonationShelf, 'hasVideoOwner:', hasVideoOwner);
  // If sidebar has any non-related content, return false
  if (hasChat || hasPlaylist || hasMerchShelf || hasDonationShelf || hasVideoOwner) {
    return false;
  }

  // Check if sidebar has related videos content (specifically within sidebar)
  const hasRelatedVideos = isElementVisible('ytd-watch-next-secondary-results-renderer, ytd-compact-video-renderer');

  // Additional check: count all visible children in sidebar to ensure we're not missing anything
  const allVisibleChildren = Array.from(sidebar.children).filter(child => {
    if (child.hasAttribute('hidden')) return false;
    if (child.style.display === 'none') return false;
    const computedStyle = window.getComputedStyle(child);
    if (computedStyle.display === 'none') return false;
    if (computedStyle.visibility === 'hidden') return false;
    if (computedStyle.opacity === '0') return false;
    return true;
  });

  console.log('Total visible children in sidebar:', allVisibleChildren.length, allVisibleChildren.map(c => c.tagName));

  // Return true only if sidebar exists, has related videos, but no other content
  return !!hasRelatedVideos;
}

// Function to determine what should be hidden based on current settings
function getHidingStrategy() {
  const setting = currentSettings.hideSidebar;

  // Handle both boolean (new) and string (backward compatibility) values
  const shouldHideRelatedVideos = setting === true || setting === 'always';
  // console.log('Hiding strategy - shouldHideRelatedVideos:', shouldHideRelatedVideos);
  if (!shouldHideRelatedVideos) {
    return { hideRelatedVideos: false, hideSidebar: false };
  }

  // Hide related videos, and hide entire sidebar if no other content remains
  const hasOtherContent = !sidebarContainsOnlyRelatedVideos();
  const strategy = {
    hideRelatedVideos: true,
    hideSidebar: !hasOtherContent // Hide sidebar only if no other content left
  };

  // Auto-open live chat if sidebar is being hidden and the feature is enabled
  if (strategy.hideSidebar && currentSettings.autoOpenChat) {
    // Use setTimeout to ensure DOM is ready and avoid blocking the main thread
    setTimeout(() => {
      autoOpenLiveChat();
    }, 1000);
  }

  return strategy;
}

// Function to update sidebar styles when page changes
function updateSidebarStyles() {
  const existingStyleElement = document.getElementById('yt-reading-friendly-styles');
  if (existingStyleElement && isReadingModeEnabled) {
    // Update the existing style element with new layout styles
    existingStyleElement.textContent = css`
      ${generateFontStyles()}
      ${generateFontWeightOverrides()}
      ${generateLayoutStyles()}
      ${generateFocusStyles()}
    `;
    if (window.location.pathname.includes('/watch')) {
      existingStyleElement.textContent += css`
        #primary{
          margin-right: var(--ytd-margin-6x) !important;
        }
      `;
    }
  }
}

// Generate layout styles for hiding elements and centering content
function generateLayoutStyles() {
  return css`
    /* Hide distracting elements */
    ${(() => {
      const strategy = getHidingStrategy();
      let css = '';

      if (strategy.hideRelatedVideos) {
        css += `
        /* Hide related videos section */
        #related {
          display: none !important;
        }`;
      }

      if (strategy.hideSidebar) {
        css += `
        /* Hide entire sidebar when no other content remains */
        #secondary, #related {
          display: none !important;
        }`;
      }

      return css;
    })()}

    ${currentSettings.hideComments ? `
    #comments, ytd-comments#comments { /* Comments section */
      display: none !important;
    }` : ''}

    ${currentSettings.hideEndscreen ? `
    .ytp-endscreen-content { /* End screen suggestions */
      display: none !important;
    }` : ''}

    ${currentSettings.hideMiniGuide ? `
    /* Hide mini guide sidebar */
    body.mini-guide-visible {
      --ytd-mini-guide-width: 0px !important;
    }
    ytd-mini-guide-renderer {
      display: none !important;
    }

    ytd-app {
      --ytd-mini-guide-width: 0px !important;
    }` : ''}

    /* Guide style is handled by a separate style element */

    /* Center the main content */
    ytd-page-manager #header,ytd-two-column-browse-results-renderer,ytd-two-column-search-results-renderer,ytd-browse,#primary #below, ytd-watch-flexy[theater] #columns{
      max-width: ${currentSettings.maxWidth}px !important;
      min-width: auto !important;
      margin-left: auto !important;
      margin-right: auto !important;
      float: none !important;
      box-sizing: border-box !important;
    }
    ytd-watch-flexy[theater] #columns{
      max-width: calc(${currentSettings.maxWidth}px + var(--ytd-watch-flexy-sidebar-width)) !important;
    }
    #primary{
      padding-right: 0 !important;
    }
    ytd-browse[page-subtype=playlist] ytd-two-column-browse-results-renderer.ytd-browse{
      padding-left: 0 !important;
    }
    #author-thumbnail+#main #header,.ytd-post-renderer #header{
      margin-left: inherit !important;
      margin-right: inherit !important;
    }

    /* Improve video player container */
    /* #player {
      max-width: 100% !important;
      min-width: auto !important;
      margin: 0 auto !important;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    } */
  `;
}

// Generate focus mode styles for background dimming (Turn Off the Lights style)
function generateFocusStyles() {
  if (!currentSettings.focusMode) {
    return '';
  }

  // Calculate the dimming opacity (convert percentage to decimal)
  const dimmingOpacity = currentSettings.focusOpacity / 100;
  const isVideoPage = window.location.pathname.includes('/watch');

  return css`
    /* Turn Off the Lights style - dim everything except main content */

    /* Create dark overlay that covers the entire page */
    body::before {
      content: "";
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, ${dimmingOpacity});
      pointer-events: auto; /* Enable clicks to disable focus mode */
      z-index: 2147483640; /* Very high z-index but below max */
      transition: opacity 0.3s ease-in-out;
      cursor: pointer;
    }

    ${isVideoPage ? `
      /* Video page specific styles */

      /* Keep video player and its container visible */
      .html5-video-player{
        position: relative;
        z-index: 2147483641 !important;
      }

    ` : `
      /* Non-video pages - keep main content visible */

      /* Keep main content areas visible */
      #primary,
      #secondary,
      ytd-two-column-browse-results-renderer,
      ytd-two-column-search-results-renderer,
      #contents.ytd-rich-grid-renderer,
      ytd-browse[page-subtype="home"] #primary,
      ytd-search #primary {
        position: relative;
        z-index: 2147483641 !important;
      }
    `}

    /* Always keep these UI elements visible */

    /* Top navigation bar */
    ytd-masthead,
    #masthead-container {
      position: relative;
      z-index: 2147483641 !important;
      opacity: 0.95;
    }

    /* Video player controls and overlays */
    .html5-video-player,
    .ytp-chrome-bottom,
    .ytp-chrome-top,
    .ytp-gradient-bottom,
    .ytp-gradient-top,
    .ytp-popup,
    .ytp-settings-menu,
    .ytp-panel-menu,
    .ytp-menuitem,
    .ytp-volume-panel,
    .ytp-tooltip,
    .ytp-contextmenu {
      z-index: 2147483642 !important;
    }

    /* Side navigation when open */
    tp-yt-app-drawer[opened],
    #guide[opened],
    ytd-mini-guide-renderer {
      position: relative;
      z-index: 2147483641 !important;
    }

    /* Popups, modals, and dropdowns */
    ytd-popup-container,
    ytd-menu-popup-renderer,
    iron-dropdown,
    paper-dialog,
    ytd-dialog-manager,
    .ytp-popup,
    .ytp-settings-menu,
    .ytp-panel-menu,
    ytd-notification-action-renderer,
    ytd-notification-renderer {
      z-index: 2147483643 !important;
    }

    /* Search suggestions and autocomplete */
    #search-container ytd-searchbox,
    ytd-search-suggestions-renderer,
    .sbsb_a {
      z-index: 2147483643 !important;
    }

    /* Smooth transitions */
    #player-container,
    #primary,
    #secondary {
      transition: box-shadow 0.3s ease-in-out, background 0.3s ease-in-out;
    }

    /* Ensure focus mode doesn't interfere with scrolling */
    body {
      overflow: auto !important;
    }
  `;
}

// Apply focus mode styles independently
function applyFocusMode() {
  // Remove any existing focus mode styles first
  removeFocusMode();

  // Only apply if focus mode is enabled
  if (!currentSettings.focusMode) {
    return;
  }

  // Create and append focus mode style element
  const focusStyleElement = document.createElement('style');
  focusStyleElement.id = 'yt-reading-friendly-focus-styles';
  focusStyleElement.textContent = generateFocusStyles();
  document.head.appendChild(focusStyleElement);

  // Add focus mode indicator
  addFocusModeIndicator();

  // Add click listener to disable focus mode when clicking on dimmed background
  addFocusModeClickListener();
}

// Add a visual indicator that focus mode is active
function addFocusModeIndicator() {
  // Remove existing indicator
  const existingIndicator = document.getElementById('yt-focus-mode-indicator');
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // Only add if focus mode is enabled
  if (!currentSettings.focusMode) {
    return;
  }

  const indicator = document.createElement('div');
  indicator.id = 'yt-focus-mode-indicator';
  indicator.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-family: 'YouTube Sans', 'Roboto', sans-serif;
      z-index: 2147483644;
      opacity: 0.8;
      pointer-events: none;
      transition: opacity 0.3s ease;
    ">
      🔦 Focus Mode
    </div>
  `;
  document.body.appendChild(indicator);

  // Auto-hide the indicator after 3 seconds
  setTimeout(() => {
    if (indicator && indicator.parentNode) {
      indicator.style.opacity = '0';
      setTimeout(() => {
        if (indicator && indicator.parentNode) {
          indicator.remove();
        }
      }, 300);
    }
  }, 3000);
}

// Add click listener to disable focus mode when clicking on dimmed background
function addFocusModeClickListener() {
  // Remove any existing listener first
  removeFocusModeClickListener();

  // Only add if focus mode is enabled
  if (!currentSettings.focusMode) {
    return;
  }

  // Create the click handler function
  window.focusModeClickHandler = function(event) {
    // Check if the click is on the dimmed background (body::before pseudo-element)
    // Since we can't directly detect clicks on pseudo-elements, we'll check if the click
    // is on an area that should be dimmed (not on main content)

    const target = event.target;
    const isVideoPage = window.location.pathname.includes('/watch');

    // Define main content selectors that should NOT disable focus mode when clicked
    const mainContentSelectors = [
      // Video player and controls
      '#player-container', '#player', '#movie_player', '.html5-video-player',
      '.ytp-chrome-bottom', '.ytp-chrome-top', '.ytp-popup', '.ytp-settings-menu',

      // Main content areas
      '#primary', '#secondary', '#masthead-container', 'ytd-masthead',

      // Navigation and UI
      'tp-yt-app-drawer', '#guide', 'ytd-mini-guide-renderer',
      'ytd-popup-container', 'ytd-menu-popup-renderer',

      // Video page specific
      ...(isVideoPage ? [
        '#primary.ytd-watch-flexy', '#secondary.ytd-watch-flexy',
        '#columns.ytd-watch-flexy #primary'
      ] : [
        // Non-video pages
        'ytd-two-column-browse-results-renderer',
        'ytd-two-column-search-results-renderer',
        '#contents.ytd-rich-grid-renderer'
      ])
    ];

    // Check if the clicked element or any of its parents match main content selectors
    let element = target;
    while (element && element !== document.body) {
      for (const selector of mainContentSelectors) {
        try {
          if (element.matches && element.matches(selector)) {
            return; // Don't disable focus mode - clicked on main content
          }
        } catch (e) {
          // Ignore invalid selectors
        }
      }
      element = element.parentElement;
    }

    // If we get here, the click was on the dimmed background area
    // Disable focus mode
    event.preventDefault();
    event.stopPropagation();

    if (currentSettings.focusMode) {
      toggleFocusMode();
    }
  };

  // Add the click listener to the document
  document.addEventListener('click', window.focusModeClickHandler, true);
}

// Remove focus mode click listener
function removeFocusModeClickListener() {
  if (window.focusModeClickHandler) {
    document.removeEventListener('click', window.focusModeClickHandler, true);
    window.focusModeClickHandler = null;
  }
}

// Remove focus mode styles
function removeFocusMode() {
  const existingFocusStyle = document.getElementById('yt-reading-friendly-focus-styles');
  if (existingFocusStyle) {
    existingFocusStyle.remove();
  }

  // Also remove the focus mode indicator
  const existingIndicator = document.getElementById('yt-focus-mode-indicator');
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // Remove the click listener
  removeFocusModeClickListener();
}

// Apply reading mode styles
function applyReadingMode() {
  // Remove any existing styles first
  removeReadingMode();

  // Apply HTML font size
  applyHtmlFontSize();

  // Create and append style element
  const styleElement = document.createElement('style');
  styleElement.id = 'yt-reading-friendly-styles';

  // Build CSS by combining all style sections
  styleElement.textContent = css`
    ${generateFontStyles()}
    ${generateFontWeightOverrides()}
    ${generateLayoutStyles()}
    ${generateFocusStyles()}
  `;
  if (window.location.pathname.includes('/watch')) {
    styleElement.textContent += css`
      #primary{
        margin-right: var(--ytd-margin-6x) !important;
      }
    `;
  }

  document.head.appendChild(styleElement);

  // Apply guide style
  // applyGuideStyle();
}
function disableDefaultGuideStyle(ref){
  console.log('Disabling default guide style from', ref);
  // Get the ytd-app element
  const ytdApp = document.querySelector('ytd-app');
  if (!ytdApp) return;
  // Check if we're on a video page
  const isVideoPage = window.location.pathname.includes('/watch');
   // Remove any existing guide style element
  const existingGuideStyle = document.getElementById('yt-reading-friendly-guide-styles');
  if (existingGuideStyle) {
    existingGuideStyle.remove();
  }
  if (currentSettings.guideStyle === 'push' && isVideoPage) {
    disableOverlayStyle();
  }else if (currentSettings.guideStyle === 'overlay' && !isVideoPage){
    disablePushStyle();
  }
}

function disableOverlayStyle(){
  const guideStyleElement = document.createElement('style');
  guideStyleElement.id = 'yt-reading-friendly-guide-styles';
  guideStyleElement.textContent = css`
    #scrim{
      display:none !important;
      transition-duration: 0ms !important;
    }
    tpYtAppDrawer{
      transition-duration: 0ms !important;
    }
    #contentContainer{
      transition-duration: 0ms !important;
    }
    ytd-app[fullscreen] tp-yt-app-drawer{
        display: none !important;
    }
    ytd-app[fullscreen] ytd-page-manager.ytd-app{
        margin-left: 0 !important;
    }
    ytd-app:not([mini-guide-visible]):not([guide-persistent-and-visible]):not([fullscreen]) ytd-page-manager.ytd-app{
      margin-left: var(--ytd-mini-guide-width) !important;
    }
    ytd-browse:not([mini-guide-visible]) ytd-playlist-header-renderer.ytd-browse,
    ytd-browse:not([mini-guide-visible]) ytd-playlist-sidebar-renderer.ytd-browse,
    ytd-browse:not([mini-guide-visible]) .page-header-sidebar.ytd-browse{
      left: var(--ytd-mini-guide-width) !important;
    }
  `;
  document.head.appendChild(guideStyleElement);
  resetStyleOnVideoPage();
}
function disablePushStyle(){
  // Create a new style element for guide styles
  const guideStyleElement = document.createElement('style');
  guideStyleElement.id = 'yt-reading-friendly-guide-styles';
  guideStyleElement.textContent = css`
    ytd-app[guide-persistent-and-visible] ytd-page-manager.ytd-app {
        margin-left: 0 !important;
    }
    ytd-app:not([mini-guide-visible]) ytd-page-manager.ytd-app{
      margin-left: var(--ytd-mini-guide-width) !important;
    }
    ytd-browse:not([mini-guide-visible]) ytd-playlist-header-renderer.ytd-browse,
    ytd-browse:not([mini-guide-visible]) ytd-playlist-sidebar-renderer.ytd-browse,
    ytd-browse:not([mini-guide-visible]) .page-header-sidebar.ytd-browse{
      left: var(--ytd-mini-guide-width) !important;
    }
  `;
  document.head.appendChild(guideStyleElement);
  resetStyleOnGeneralPage();
}

// Apply guide style with custom CSS overrides
function applyGuideStyle(ref) {
  console.log('Applying guide style from', ref);
  // Get the ytd-app element
  const ytdApp = document.querySelector('ytd-app');
  if (!ytdApp) return;

  // Check if we're on a video page
  const isVideoPage = window.location.pathname.includes('/watch');
  const guideElement = document.querySelector('tp-yt-app-drawer');
  const isGuideOpen = guideElement.hasAttribute('opened');

  if (currentSettings.guideStyle === 'push') {
    console.log('Preserved guide state:', preservedGuideState);
    if (isVideoPage) {
      // run this only on theater mode
      if (document.querySelector('ytd-watch-flexy[theater]')) {
        rebuildPlayer();
      }

      if(preservedGuideState === 'opened'|| (isGuideOpen && preservedGuideState === null)){
        setAttributeIfNotExist(guideElement, 'opened');
        resetStyleOnVideoPage();
        ytdApp.removeAttribute('mini-guide-visible');
      }else{
         if (!currentSettings.hideMiniGuide) {
          setAttributeIfNotExist(ytdApp, 'mini-guide-visible');
        }
        ytdApp.removeAttribute('guide-persistent-and-visible');
        guideElement.removeAttribute('opened');
      }

      // Check if we have a preserved guide state from non-video page and guide is currently closed
      // if (preservedGuideState === 'opened' && !isGuideOpen) {
      //   console.log('Restoring guide state on video page (push style)');
      //   setAttributeIfNotExist(guideElement, 'opened');
      //   resetStyleOnVideoPage();
      //   ytdApp.removeAttribute('mini-guide-visible');
      // } else if (isGuideOpen) {
      //   console.log('Guide is opened on video page (push style)');
      //   resetStyleOnVideoPage();
      //   ytdApp.removeAttribute('mini-guide-visible');
      //   setAttributeIfNotExist(guideElement, 'opened');
      // } else {
      //   console.log('Guide is closed on video page (push style)');
      //   // if setting mini guide is true
      //   if (!currentSettings.hideMiniGuide) {
      //     setAttributeIfNotExist(ytdApp, 'mini-guide-visible');
      //   }
      //   ytdApp.removeAttribute('guide-persistent-and-visible');
      //   guideElement.removeAttribute('opened');
      // }
    } else {
      // Non-video pages with push style - apply standard behavior
      console.log('Non-video page with push style, guide state:', isGuideOpen ? 'opened' : 'closed');
      if(preservedGuideState === 'opened'|| (isGuideOpen && preservedGuideState === null)){
        setAttributeIfNotExist(guideElement, 'opened');
        ytdApp.removeAttribute('mini-guide-visible');
        setAttributeIfNotExist(ytdApp, 'guide-persistent-and-visible');
      }else{
         if (!currentSettings.hideMiniGuide) {
          setAttributeIfNotExist(ytdApp, 'mini-guide-visible');
        }
        guideElement.removeAttribute('opened');
      }
    }
  } else if (currentSettings.guideStyle === 'overlay') {
    // For overlay style, handle both video and non-video pages
    if (isVideoPage) {
      // On video pages with overlay style, preserve the guide state from non-video pages
      return;
    }
    // Non-video pages with overlay style
    if (isGuideOpen) {
      console.log('Guide is opened');
      const contentContainer = document.querySelector('#contentContainer');
      if (contentContainer) {
        contentContainer.style.transitionDuration = '200ms';
      }
      const scrim = document.querySelector('#scrim');
      if (scrim) {
        scrim.classList.add('visible');
        scrim.style.transitionDuration = '200ms';
      }
      //remove persistent attribute of tp-yt-app-drawer
      if (guideElement) {
        guideElement.removeAttribute('persistent');
      }

      // Remove guide-persistent-and-visible attribute
      ytdApp.removeAttribute('guide-persistent-and-visible');
      // ytdApp.setAttribute('mini-guide-visible', '');
      // add body style
      document.body.style.top = '0px';
      // add body class
      document.body.classList.add('lock-scrollbar');
    }else{
      console.log('Guide is closed');
      // reverse action
      resetStyleOnGeneralPage();
    }
  }
}
function setAttributeIfNotExist(element, attribute){
  console.log("setAttributeIfNotExist:", element, attribute);
  console.log("hasAttribute:", element.hasAttribute(attribute));
  if (!element.hasAttribute(attribute)){
    console.log("setAttributeIfNotExist: setting attribute",attribute);
    element.setAttribute(attribute, '');
  }
}
function resetStyleOnVideoPage(){
  const ytdApp = document.querySelector('ytd-app');
  if (!ytdApp) return;
  setAttributeIfNotExist(ytdApp, 'guide-persistent-and-visible');
  document.body.classList.remove('lock-scrollbar');
  const contentContainer = document.querySelector('#contentContainer');
  if (contentContainer) {
    contentContainer.style.transitionDuration = '0ms';
  }
  const scrim = document.querySelector('#scrim');
  if (scrim) {
    scrim.classList.remove('visible');
    scrim.style.transitionDuration = '0ms';
  }
  const tpYtAppDrawer = document.querySelector('tp-yt-app-drawer');
  if (tpYtAppDrawer) {
    tpYtAppDrawer.style.transitionDuration = '0ms';
    setAttributeIfNotExist(tpYtAppDrawer, 'persistent');
    setAttributeIfNotExist(tpYtAppDrawer, 'opened');
  }
}

function resetStyleOnGeneralPage() {
  const ytdApp = document.querySelector('ytd-app');
  if (!ytdApp) return;
  const scrim = document.querySelector('#scrim');
  if (scrim) {
    scrim.classList.remove('visible');
  }
  //add persistent attribute of tp-yt-app-drawer
  const tpYtAppDrawer = document.querySelector('tp-yt-app-drawer');
  if (tpYtAppDrawer) {
    setAttributeIfNotExist(tpYtAppDrawer, 'persistent');
  }

  // Add guide-persistent-and-visible attribute
  // ytdApp.setAttribute('guide-persistent-and-visible', '');
  setAttributeIfNotExist(ytdApp, 'mini-guide-visible');
  tpYtAppDrawer.removeAttribute('opened');
  // remove body style
  document.body.style.top = '';
  // remove body class
  document.body.classList.remove('lock-scrollbar');
}

// Apply text scaling
function applyHtmlFontSize() {
  const pixelSize = htmlFontSizes[currentSettings.htmlFontSize];
  // Apply pixel-based font size to the HTML element
  document.documentElement.style.fontSize = pixelSize;
}

// Remove reading mode styles
function removeReadingMode() {
  const existingStyle = document.getElementById('yt-reading-friendly-styles');
  if (existingStyle) {
    existingStyle.remove();
  }

  // Reset HTML font size to default if reading mode is disabled
  if (!isReadingModeEnabled) {
    document.documentElement.style.fontSize = '';

    // Maintain guide style even when reading mode is disabled
    // applyGuideStyle();
  }
}

// Apply featured page styles
function applyFeaturedPageStyles() {
  // Check if we're on a featured page by looking for specific elements and attributes
  // This is more reliable than just checking the URL

  // First, look for the channel page elements
  const channelPage = document.querySelector('ytd-browse[page-subtype="channels"]');

  // Then check if we're on a featured tab by looking for the tab element or content structure
  const featuredTab = document.querySelector('tp-yt-paper-tab.iron-selected[role="tab"]:not([hidden]) a[href*="featured"]');
  const featuredContent = document.querySelector('ytd-item-section-renderer[page-subtype=channels]');

  // Check if we're on a playlist page (we don't want to apply featured styles there)
  const isPlaylistPage = document.querySelector('ytd-browse[page-subtype="playlist"]') ||
                         window.location.href.includes('/playlist');

  // If we're on a channel page and either the featured tab is selected or featured content is visible
  // AND we're not on a playlist page
  if (channelPage && (featuredTab || featuredContent) && !isPlaylistPage) {
    console.log('Detected featured page, applying styles');

    // Apply styles to the channel contents
    const channelContents = document.querySelectorAll('ytd-item-section-renderer[page-subtype=channels] #contents.style-scope.ytd-item-section-renderer.style-scope.ytd-item-section-renderer');
    channelContents.forEach(element => {
      element.classList.add('featured-page-style');
    });
  } else {
    // Remove styles if we're not on a featured page or if we're on a playlist page
    const styledElements = document.querySelectorAll('.featured-page-style');
    styledElements.forEach(element => {
      element.classList.remove('featured-page-style');
    });

    if (isPlaylistPage) {
      console.log('Detected playlist page, removing featured styles');
    }
  }

  // Apply margin-top to contents if there's no header
  applyMarginToContentsWithoutHeader();
}

// Apply margin-top to .ytd-browse #contents.style-scope.ytd-rich-grid-renderer if there's no header before it
function applyMarginToContentsWithoutHeader() {
  // Find all content elements that match the selector
  const contentElements = document.querySelectorAll('.ytd-browse #contents.style-scope.ytd-rich-grid-renderer');

  contentElements.forEach(contentElement => {
    // Get the parent ytd-rich-grid-renderer element
    const gridRenderer = contentElement.closest('ytd-rich-grid-renderer');

    if (gridRenderer) {
      // Check if there's a header element before the contents
      const header = gridRenderer.querySelector('#header');

      // Also check for header-container which is another type of header YouTube might use
      const headerContainer = gridRenderer.querySelector('.header-container') ||
                             contentElement.closest('ytd-browse')?.querySelector('.header-container');

      // Check if either header is present and visible
      const hasVisibleHeader =
        (header && header.offsetHeight > 0 && getComputedStyle(header).display !== 'none') ||
        (headerContainer && headerContainer.offsetHeight > 0 && getComputedStyle(headerContainer).display !== 'none');

      // If no header is found or all headers are hidden, add margin-top
      if (!hasVisibleHeader) {
        contentElement.style.marginTop = '48px';
        console.log('Added margin-top to content element because no header was found');
      } else {
        // If any header exists and is visible, remove the margin-top
        contentElement.style.marginTop = '';
        console.log('Removed margin-top from content element because a header was found');
      }
    }
  });

  // Also check for other content elements that might need margin
  const otherContentElements = document.querySelectorAll('ytd-browse:not([page-subtype="channels"]) ytd-rich-grid-renderer #contents');

  otherContentElements.forEach(contentElement => {
    // Check for any header or header-container in the parent elements
    const browse = contentElement.closest('ytd-browse');
    const gridRenderer = contentElement.closest('ytd-rich-grid-renderer');

    if (browse && gridRenderer) {
      // Check for headers at different levels
      const header = gridRenderer.querySelector('#header');
      const headerContainer = gridRenderer.querySelector('.header-container') ||
                             browse.querySelector('.header-container');

      // Check if either header is present and visible
      const hasVisibleHeader =
        (header && header.offsetHeight > 0 && getComputedStyle(header).display !== 'none') ||
        (headerContainer && headerContainer.offsetHeight > 0 && getComputedStyle(headerContainer).display !== 'none');

      // If no header is found or all headers are hidden, add margin-top
      if (!hasVisibleHeader) {
        contentElement.style.marginTop = '48px';
        console.log('Added margin-top to other content element because no header was found');
      } else {
        // If any header exists and is visible, remove the margin-top
        contentElement.style.marginTop = '';
      }
    }
  });
}

// Observe changes to the guide state
function observeGuideState() {

  //observe tp-yt-app-drawer's opened attribute
  const tpYtAppDrawer = document.querySelector('tp-yt-app-drawer');
  if (!tpYtAppDrawer) {
    // If not found, try again later
    setTimeout(observeGuideState, 100);
    return;
  }

  const observer = new MutationObserver((mutations) => {
    // Check if the opened attribute changed
    const guideStateChanged = mutations.some(mutation =>
      mutation.type === 'attributes' &&
      (mutation.attributeName === 'opened' ||
      mutation.attributeName === 'persistent')
    );
    console.log(mutations);
    if (guideStateChanged) {
      // Apply our guide style to ensure consistent behavior
      // Small delay to let YouTube's own scripts run first
      setTimeout(() => {
        applyGuideStyle("observeGuideState");
      }, 200);
    }
  });

  // Start observing the tp-yt-app-drawer element for attribute changes
  observer.observe(tpYtAppDrawer, { attributes: true });

  // // Get the ytd-app element
  const ytdApp = document.querySelector('ytd-app');
  if (!ytdApp) {
    // If not found, try again later
    setTimeout(observeGuideState, 1000);
    return;
  }

  // // Create a MutationObserver to watch for attribute changes
  const observer2 = new MutationObserver((mutations) => {
    // Check if tp-yt-app-drawer's opened attribute changed
    console.log(mutations);
    const guideStateChanged = mutations.some(mutation =>
      mutation.type === 'attributes' &&
      mutation.attributeName === 'guide-persistent-and-visible'
    );
    if (guideStateChanged) {
      // Apply our guide style to ensure consistent behavior
      setTimeout(() => {
        applyGuideStyle("guideStateChanged");
      }, 200);
    }
  });

  // // Start observing the ytd-app element for attribute changes
  observer2.observe(ytdApp, { attributes: true });

  console.log('Added observer for guide state changes');
}

// Add click listeners to ensure our styles are applied when guide is toggled
function addGuideButtonListener() {
  // add listener to scrim
  const scrim = document.querySelector('#scrim');
  if (scrim) {
    scrim.addEventListener('click', () => {
      // Small delay to let YouTube's own scripts run first
      setTimeout(() => {
        const guideButton = document.querySelector('#guide-button button, #guide-button a');
        if (guideButton) {
          guideButton.click();
          guideButton.click();
        }
      }, 200);
    });
  }

  // Find the guide button (hamburger menu)
  const guideButton = document.querySelector('#guide-button button, #guide-button a');

  if (guideButton) {
    guideButton.addEventListener('click', () => {
      console.log("############ Guild button is click")
      // Small delay to let YouTube's own scripts run first
      if (currentSettings.guideStyle === 'push') {
        const guideElement = document.querySelector('tp-yt-app-drawer');
        if (guideElement.hasAttribute('opened')) {
          console.log('toggle to closed');
          preservedGuideState = 'closed';
          setTimeout(() => {
            guideElement.removeAttribute('opened');
          }, 100);
        }else{
          console.log('toggle to opened');
          preservedGuideState = 'opened';
          setTimeout(() => {
            setAttributeIfNotExist(guideElement, 'opened');
          }, 100);
        }
      }
      setTimeout(() => {
        applyGuideStyle("addGuideButtonListener");
      }, 200);
    });

    if (currentSettings.guideStyle === 'overlay'&& !window.location.pathname.includes('/watch')) {
      // add listener to guide inner content
      const guideInnerContent = document.querySelector('#guide-inner-content');
      if (guideInnerContent) {
        guideInnerContent.addEventListener('click', () => {
          // Small delay to let YouTube's own scripts run first
          setTimeout(() => {
            guideButton.click();
          }, 100);
        });
      }
    }
    console.log('Added click listeners for guide interactions');
  } else {
    // If button not found, try again later
    setTimeout(addGuideButtonListener, 1000);
  }
}

function rebuildPlayer() {
  const html5VideoContainer = document.querySelector('.html5-video-container');
  if (html5VideoContainer) {
    html5VideoContainer.style.display = 'none';
  }
  const ytpSpinner = document.querySelector('.ytp-spinner');
  if (ytpSpinner) {
    ytpSpinner.style.display = 'block';
  }
  setTimeout(() => {
    window.dispatchEvent(new Event('resize'));
    setTimeout(() => {
      if (html5VideoContainer) {
        html5VideoContainer.style.display = 'block';
      }
      if (ytpSpinner) {
        ytpSpinner.style.display = 'none';
      }
    }, 200);
  }, 200);
}


// Observe page changes to handle YouTube's SPA navigation
function observePageChanges() {
  // Create a MutationObserver to watch for page changes
  const observer = new MutationObserver(function(mutations) {
    // Check if this is a significant DOM change
    const significantChange = mutations.some(mutation =>
      mutation.addedNodes.length > 0 &&
      Array.from(mutation.addedNodes).some(node =>
        node.nodeType === 1 && (
          node.tagName === 'YTD-BROWSE' ||
          node.tagName === 'YTD-ITEM-SECTION-RENDERER' ||
          node.tagName === 'TP-YT-PAPER-TAB'
        )
      )
    );

    if (significantChange) {
      // Small delay to ensure YouTube's own scripts have finished
      setTimeout(() => {
        // Apply featured page styles regardless of reading mode
        applyFeaturedPageStyles();

        // Also check for contents without headers
        applyMarginToContentsWithoutHeader();

        // Apply guide style regardless of reading mode
        disableDefaultGuideStyle("significantChange");
        // click guide button
        if (currentSettings.guideStyle === 'push' && window.location.pathname.includes('/watch')) {
          doubleClickGuideButtonForTriggerYtScript();
        }
      }, 200);
    }

    if (isReadingModeEnabled) {
      // Check if we're on a video page
      if (window.location.pathname.includes('/watch')) {
        // Small delay to ensure YouTube's own scripts have finished
        setTimeout(() => {
          applyReadingMode();
          // Update sidebar styles in case content changed
          updateSidebarStyles();
        }, 200);
      }
    }

    // Check if we navigated to a video page and should auto-open chat
    if (window.location.pathname.includes('/watch') && currentSettings.autoOpenChat) {
      const strategy = getHidingStrategy();
      if (strategy.hideSidebar) {
        // Longer delay for page navigation to ensure all elements are loaded
        setTimeout(() => {
          autoOpenLiveChat();
        }, 2000);
      }
    }
  });

  // Start observing the document body for changes
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // Also listen for URL changes since YouTube is a SPA
  let lastUrl = location.href;


  // Capture guide state before navigation by listening to clicks on navigation elements
  function captureGuideStateBeforeNavigation() {
    // Listen for clicks on video thumbnails, channel links, etc.
    document.addEventListener('click', (event) => {
      const target = event.target.closest('a[href*="/watch"], a[href*="/channel"], a[href*="/c/"], a[href*="/@"], a[href*="/playlist"], ytd-thumbnail, ytd-video-renderer, ytd-compact-video-renderer, ytd-grid-video-renderer, ytd-rich-grid-media, #video-title, .ytd-video-meta-block');

      if (target && currentSettings.guideStyle === 'push') {
        // Capture current guide state before navigation
        const currentGuideState = getCurrentGuideState();
        if (currentGuideState) {
          preservedGuideState = currentGuideState;
          console.log('Captured guide state before navigation via click:', preservedGuideState);
        }
      }
    }, true); // Use capture phase to ensure we get the event before navigation

    // Also capture state when using browser back/forward buttons
    window.addEventListener('popstate', () => {
      if (currentSettings.guideStyle === 'push') {
        const currentGuideState = getCurrentGuideState();
        if (currentGuideState) {
          preservedGuideState = currentGuideState;
          console.log('Captured guide state before popstate navigation:', preservedGuideState);
        }
      }
    });
  }

  // Initialize the navigation listener
  captureGuideStateBeforeNavigation();

  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      const wasVideoPage = lastUrl.includes('/watch');
      const isVideoPage = url.includes('/watch');
      console.log('Page transition detected:', {
        from: lastUrl,
        to: url,
        wasVideoPage,
        isVideoPage,
        guideStyle: currentSettings.guideStyle,
        preservedGuideState: preservedGuideState
      });

      // Handle page transitions for push style
      if (currentSettings.guideStyle === 'push') {
        if (!wasVideoPage && isVideoPage) {
          // Transitioning from non-video to video page with push style
          console.log('Transitioning to video page, preserved guide state:', preservedGuideState);
          // if (preservedGuideState === 'opened') {
            // Restore the guide state on video page
            setTimeout(() => {
              // click guide button
              // const guideButton = document.querySelector('#guide-button button, #guide-button a');
              // if (guideButton) {
              //   setTimeout(() => {
              //     guideButton.click();
              //   }, 200);
              // }
              applyGuideStyle('urlChangeRestore');
            }, 200);
          // }
        }
      }
      lastUrl = url;
      setTimeout(() => {
        applyFeaturedPageStyles();
        applyMarginToContentsWithoutHeader();
        disableDefaultGuideStyle("urlChange");
        // Apply focus mode on page changes
        applyFocusMode();
        // Update sidebar styles in case content changed
        if (isReadingModeEnabled) {
          updateSidebarStyles();
        }
      }, 200);
    }
  }).observe(document, {subtree: true, childList: true});
}

// Initialize when the DOM is fully loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}

function doubleClickGuideButtonForTriggerYtScript() {
  console.log('doubleClickGuideButtonForTriggerYtScript');
  const guideButton = document.querySelector('#guide-button button, #guide-button a');
  if (guideButton && !isTriggeredFromClick) {
    console.log('Clicking guide button');
    isTriggeredFromClick = true;
    setTimeout(() => {
      guideButton.click();
      setTimeout(() => {
        guideButton.click();
      }, 100);
    }, 100);
  }
}

