<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Reading Friendly - Options</title>

  <link rel="stylesheet" href="options.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>YouTube Reading Friendly</h1>
      <p class="subtitle">Advanced Options</p>
    </header>

    <main>
      <section class="card">
        <h2>Reading Experience</h2>

        <div class="option-group">
          <h3>Font Settings</h3>

          <div class="option-row">
            <label for="font-family">Font Family:</label>
            <select id="font-family">
              <option value="system">System Default</option>
              <option value="youtube-sans" selected>YouTube Sans</option>
              <option value="serif">Serif</option>
              <option value="sans-serif">Sans-serif</option>
              <option value="monospace">Monospace</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div class="option-row">
            <label for="custom-font">Custom Font:</label>
            <input type="text" id="custom-font" placeholder="e.g., Georgia, Times New Roman">
          </div>

          <div class="option-row">
            <label for="line-height">Line Height:</label>
            <select id="line-height">
              <option value="tight">Tight</option>
              <option value="normal" selected>Normal</option>
              <option value="relaxed">Relaxed</option>
            </select>
          </div>

        </div>
        <div class="option-group">
          <h3>Font Weight Settings:</h3>
          <div class="option-row">
            <label for="font-weight-title">Video Title:</label>
            <select id="font-weight-title">
              <option value="thin">Thin</option>
              <option value="normal">Normal</option>
              <option value="bold" selected>Bold</option>
            </select>
          </div>
          <div class="option-row">
            <label for="font-weight-description">Video Description:</label>
            <select id="font-weight-description">
              <option value="thin">Thin</option>
              <option value="normal" selected>Normal</option>
              <option value="bold">Bold</option>
            </select>
          </div>
          <div class="option-row">
            <label for="font-weight-comments">Comments:</label>
            <select id="font-weight-comments">
              <option value="thin">Thin</option>
              <option value="normal" selected>Normal</option>
              <option value="bold">Bold</option>
            </select>
          </div>
          <div class="option-row">
            <label for="bold-font-weight">Bold Font Weight:</label>
            <select id="bold-font-weight">
              <option value="700" selected>Heavy (700)</option>
              <option value="500">Medium (500)</option>
            </select>
          </div>
        </div>

        <div class="option-group">
          <h3>Layout Settings</h3>

          <div class="option-row">
            <label for="max-width">Content Max Width:</label>
            <div class="ant-slider-container">
              <div class="ant-slider ant-slider-with-marks" id="max-width-slider">
                <div class="ant-slider-rail"></div>
                <div class="ant-slider-track" id="max-width-track"></div>
                <div class="ant-slider-step">
                  <span class="ant-slider-dot ant-slider-dot-active" style="left: 0%;"></span>
                  <span class="ant-slider-dot" style="left: 20%;"></span>
                  <span class="ant-slider-dot" style="left: 40%;"></span>
                  <span class="ant-slider-dot" style="left: 60%;"></span>
                  <span class="ant-slider-dot" style="left: 80%;"></span>
                  <span class="ant-slider-dot" style="left: 100%;"></span>
                </div>
                <div class="ant-slider-handle" id="max-width-handle" style="left: 0%;"></div>
                <div class="ant-slider-mark">
                  <span class="ant-slider-mark-text" style="left: 0%;">720px</span>
                  <span class="ant-slider-mark-text" style="left: 20%;">960px</span>
                  <span class="ant-slider-mark-text" style="left: 40%;">1200px</span>
                  <span class="ant-slider-mark-text" style="left: 60%;">1440px</span>
                  <span class="ant-slider-mark-text" style="left: 80%;">1680px</span>
                  <span class="ant-slider-mark-text" style="left: 100%;">1920px</span>
                </div>
              </div>
              <input type="range" id="max-width" min="720" max="1920" step="240" style="opacity: 0; position: absolute; width: 100%; height: 100%; cursor: pointer;">
            </div>
            <input type="text" id="max-width-value" class="value-input">
          </div>
        </div>
      </section>

      <section class="card">
        <h2>Content Filtering</h2>

        <div class="option-group">
          <h3>Hide Elements</h3>

          <div class="checkbox-row">
            <input type="checkbox" id="hide-sidebar" checked>
            <label for="hide-sidebar">
              Hide Related videos Sidebar
            </label>
          </div>

          <div class="checkbox-row">
            <input type="checkbox" id="auto-open-chat">
            <label for="auto-open-chat">
              Auto-open live chat
            </label>
          </div>

          <div class="checkbox-row">
            <input type="checkbox" id="hide-comments" checked>
            <label for="hide-comments">Hide Comments</label>
          </div>

          <div class="checkbox-row">
            <input type="checkbox" id="hide-endscreen" checked>
            <label for="hide-endscreen">Hide End Screen Suggestions</label>
          </div>

          <div class="checkbox-row">
            <input type="checkbox" id="hide-mini-guide" checked>
            <label for="hide-mini-guide">Hide Mini Navigation Sidebar</label>
          </div>

          
        </div>
      </section>

      

      <section class="card">
        <h2>Advanced Settings</h2>
        <div class="option-group">
          <h3>Navigation Sidebar Behavior</h3>
        <div class="option-row">
            <label for="guide-style">Opening Style:</label>
            <select id="guide-style">
              <option value="default">Default (YouTube's behavior)</option>
              <option value="overlay" selected>Always Overlay (Behave like navigation sidebar at video page)</option>
              <!-- This option is not working so smoothly so we not exposing it for now -->
              <!-- <option value="push">Push (moves content to right)</option>-->
            </select>
          </div>
        </div>
        <div class="option-group">
          <h3>Focus Mode Settings</h3>
          <p>Focus mode is a feature that dims the background to help you focus on the main content(ex. video player).<br/>
            You can toggle focus mode by keyboard shortcut which set on settings(see below) or clicking on extension icon in toolbar.
          </p>
          <div class="option-row">
            <label for="focus-opacity">Background Dimming:</label>
            <div class="ant-slider-container">
              <div class="ant-slider ant-slider-with-marks" id="focus-opacity-slider">
                <div class="ant-slider-rail"></div>
                <div class="ant-slider-track" id="focus-opacity-track"></div>
                <div class="ant-slider-step">
                  <span class="ant-slider-dot ant-slider-dot-active" style="left: 0%;"></span>
                  <span class="ant-slider-dot" style="left: 10.53%;"></span>
                  <span class="ant-slider-dot" style="left: 21.05%;"></span>
                  <span class="ant-slider-dot" style="left: 31.58%;"></span>
                  <span class="ant-slider-dot" style="left: 42.11%;"></span>
                  <span class="ant-slider-dot" style="left: 52.63%;"></span>
                  <span class="ant-slider-dot" style="left: 63.16%;"></span>
                  <span class="ant-slider-dot" style="left: 73.68%;"></span>
                  <span class="ant-slider-dot" style="left: 84.21%;"></span>
                  <span class="ant-slider-dot" style="left: 94.74%;"></span>
                  <span class="ant-slider-dot" style="left: 100%;"></span>
                </div>
                <div class="ant-slider-handle" id="focus-opacity-handle" style="left: 0%;"></div>
                <div class="ant-slider-mark">
                  <span class="ant-slider-mark-text" style="left: 0%;">0%</span>
                  <span class="ant-slider-mark-text" style="left: 10.53%;">10%</span>
                  <span class="ant-slider-mark-text" style="left: 21.05%;">20%</span>
                  <span class="ant-slider-mark-text" style="left: 31.58%;">30%</span>
                  <span class="ant-slider-mark-text" style="left: 42.11%;">40%</span>
                  <span class="ant-slider-mark-text" style="left: 52.63%;">50%</span>
                  <span class="ant-slider-mark-text" style="left: 63.16%;">60%</span>
                  <span class="ant-slider-mark-text" style="left: 73.68%;">70%</span>
                  <span class="ant-slider-mark-text" style="left: 84.21%;">80%</span>
                  <span class="ant-slider-mark-text" style="left: 94.74%;">90%</span>
                  <span class="ant-slider-mark-text" style="left: 100%;">95%</span>
                </div>
              </div>
              <input type="range" id="focus-opacity" min="0" max="95" step="5" style="opacity: 0; position: absolute; width: 100%; height: 100%; cursor: pointer;">
            </div>
            <input type="text" id="focus-opacity-value" class="value-input">
          </div>
        </div>

        <div class="option-group">
          <h3>Data Management</h3>

          <div class="button-row">
            <button id="export-settings-btn">Export Settings</button>
            <button id="import-settings-btn">Import Settings</button>
            <button id="reset-all-btn" class="danger">Reset All Settings</button>
          </div>
        </div>
      </section>

      <section class="card">
        <h2>Keyboard Shortcuts</h2>
        <p>Customize keyboard shortcuts for quick access to extension features.</p>

        <div class="option-group">
          <h3>Reading Mode</h3>

          <div class="option-row">
            <label for="toggle-shortcut">Toggle Reading Mode:</label>
            <input type="text" id="toggle-shortcut" value="Alt+R" readonly>
            <button id="change-shortcut-btn">Change</button>
          </div>
        </div>

        <div class="option-group">
          <h3>Focus Mode</h3>

          <div class="option-row">
            <label for="focus-mode-shortcut">Toggle Focus Mode:</label>
            <input type="text" id="focus-mode-shortcut" placeholder="Alt+F" readonly>
            <button type="button" id="change-focus-shortcut-btn">Change</button>
          </div>
        </div>
      </section>

      <section class="card">
        <h2>Notifications</h2>
        <p>Control when to show notifications for keyboard shortcuts and toggle actions.</p>

        <div class="option-group">
          <h3>Toggle Notifications</h3>

          <div class="checkbox-row">
            <input type="checkbox" id="reading-mode-notification">
            <label for="reading-mode-notification">Show notification when toggling Reading Mode</label>
          </div>

          <div class="checkbox-row">
            <input type="checkbox" id="focus-mode-notification">
            <label for="focus-mode-notification">Show notification when toggling Focus Mode</label>
          </div>
        </div>
      </section>
    </main>

    <footer>
      <div class="actions">
        <button id="save-options-btn" class="primary">Save Options</button>
      </div>
      <p class="version">Version 1.0.0</p>
    </footer>
  </div>

  <script src="options.js"></script>
</body>
</html>
