document.addEventListener('DOMContentLoaded', function() {
  // DOM elements
  const fontFamilySelect = document.getElementById('font-family');
  const customFontInput = document.getElementById('custom-font');
  const fontWeightTitleSelect = document.getElementById('font-weight-title');
  const fontWeightDescriptionSelect = document.getElementById('font-weight-description');
  const fontWeightCommentsSelect = document.getElementById('font-weight-comments');
  const boldFontWeightSelect = document.getElementById('bold-font-weight');
  const maxWidthRange = document.getElementById('max-width');
  const maxWidthValue = document.getElementById('max-width-value');
  const lineHeightSelect = document.getElementById('line-height');
  const hideSidebarCheckbox = document.getElementById('hide-sidebar');
  const autoOpenChatCheckbox = document.getElementById('auto-open-chat');
  const hideCommentsCheckbox = document.getElementById('hide-comments');
  const hideEndscreenCheckbox = document.getElementById('hide-endscreen');
  const hideMiniGuideCheckbox = document.getElementById('hide-mini-guide');
  const guideStyleSelect = document.getElementById('guide-style');

  const focusOpacityRange = document.getElementById('focus-opacity');
  const focusOpacityValue = document.getElementById('focus-opacity-value');
  const focusModeShortcutInput = document.getElementById('focus-mode-shortcut');
  const focusModeNotificationCheckbox = document.getElementById('focus-mode-notification');
  const changeFocusShortcutBtn = document.getElementById('change-focus-shortcut-btn');
  const toggleShortcutInput = document.getElementById('toggle-shortcut');
  const readingModeNotificationCheckbox = document.getElementById('reading-mode-notification');
  const changeShortcutBtn = document.getElementById('change-shortcut-btn');
  const exportSettingsBtn = document.getElementById('export-settings-btn');
  const importSettingsBtn = document.getElementById('import-settings-btn');
  const resetAllBtn = document.getElementById('reset-all-btn');
  const saveOptionsBtn = document.getElementById('save-options-btn');

  // Default advanced settings
  const defaultAdvancedSettings = {
    fontFamily: 'youtube-sans',
    customFont: '',
    fontWeightTitle: 'bold',
    fontWeightDescription: 'normal',
    fontWeightComments: 'normal',
    boldFontWeight: '700',
    maxWidth: 960,
    lineHeight: 'normal',
    hideSidebar: true,
    autoOpenChat: false,
    hideComments: false,
    hideEndscreen: true,
    hideMiniGuide: true,
    guideStyle: 'overlay', // 'overlay' or 'push'
    focusMode: false,
    focusOpacity: 70,
    focusModeShortcut: 'Alt+F',
    focusModeNotification: true,
    toggleShortcut: 'Alt+R',
    readingModeNotification: true
  };

  // Load saved settings
  function loadSettings() {
    chrome.storage.sync.get(defaultAdvancedSettings, function(settings) {
      // Apply settings to form elements
      fontFamilySelect.value = settings.fontFamily;
      customFontInput.value = settings.customFont;
      fontWeightTitleSelect.value = settings.fontWeightTitle;
      fontWeightDescriptionSelect.value = settings.fontWeightDescription;
      fontWeightCommentsSelect.value = settings.fontWeightComments;
      boldFontWeightSelect.value = settings.boldFontWeight || '700';
      maxWidthRange.value = settings.maxWidth;
      maxWidthValue.value = `${settings.maxWidth}px`;
      lineHeightSelect.value = settings.lineHeight;
      // Handle backward compatibility for string hideSidebar setting
      const hideSidebarValue = typeof settings.hideSidebar === 'string'
        ? (settings.hideSidebar === 'always')
        : (settings.hideSidebar !== false);
      hideSidebarCheckbox.checked = hideSidebarValue;
      autoOpenChatCheckbox.checked = settings.autoOpenChat;
      hideCommentsCheckbox.checked = settings.hideComments;
      hideEndscreenCheckbox.checked = settings.hideEndscreen;
      hideMiniGuideCheckbox.checked = settings.hideMiniGuide;
      document.getElementById('guide-style').value = settings.guideStyle || 'overlay';

      focusOpacityRange.value = settings.focusOpacity;
      focusOpacityValue.value = `${settings.focusOpacity}%`;
      focusModeShortcutInput.value = settings.focusModeShortcut;
      focusModeNotificationCheckbox.checked = settings.focusModeNotification;
      toggleShortcutInput.value = settings.toggleShortcut;
      readingModeNotificationCheckbox.checked = settings.readingModeNotification;

      // Update UI based on loaded settings
      updateUI();

      // Initialize Ant Design sliders
      // Use requestAnimationFrame to ensure DOM is fully rendered
      requestAnimationFrame(() => {
        updateAntSlider(parseInt(maxWidthRange.value));
        updateFocusOpacitySlider(parseInt(focusOpacityRange.value));
      });
    });
  }

  // Update UI based on form state
  function updateUI() {
    // Enable/disable custom font input based on font family selection
    customFontInput.disabled = fontFamilySelect.value !== 'custom';

    // Enable/disable auto-open chat based on hide sidebar setting
    autoOpenChatCheckbox.disabled = !hideSidebarCheckbox.checked;
    if (!hideSidebarCheckbox.checked) {
      autoOpenChatCheckbox.checked = false;
    }

    // Focus opacity is always enabled since focus mode checkbox was removed
  }

  // Save settings
  function saveSettings() {
    const settings = {
      fontFamily: fontFamilySelect.value,
      customFont: customFontInput.value,
      fontWeightTitle: document.getElementById('font-weight-title').value,
      fontWeightDescription: document.getElementById('font-weight-description').value,
      fontWeightComments: document.getElementById('font-weight-comments').value,
      boldFontWeight: document.getElementById('bold-font-weight').value,
      maxWidth: parseInt(maxWidthValue.value.replace(/\D/g, '')),
      lineHeight: lineHeightSelect.value,
      hideSidebar: hideSidebarCheckbox.checked,
      autoOpenChat: autoOpenChatCheckbox.checked,
      hideComments: hideCommentsCheckbox.checked,
      hideEndscreen: hideEndscreenCheckbox.checked,
      hideMiniGuide: hideMiniGuideCheckbox.checked,
      guideStyle: document.getElementById('guide-style').value,
      focusMode: true, // Always enabled since checkbox was removed
      focusOpacity: parseInt(focusOpacityRange.value),
      focusModeShortcut: focusModeShortcutInput.value,
      focusModeNotification: focusModeNotificationCheckbox.checked,
      toggleShortcut: toggleShortcutInput.value,
      readingModeNotification: readingModeNotificationCheckbox.checked
    };

    chrome.storage.sync.set(settings, function() {
      // Show save confirmation
      showNotification('Settings saved successfully!');
    });
  }

  // Reset all settings
  function resetAllSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
      chrome.storage.sync.set(defaultAdvancedSettings, function() {
        loadSettings();
        showNotification('All settings have been reset to defaults.');
      });
    }
  }

  // Export settings
  function exportSettings() {
    chrome.storage.sync.get(null, function(settings) {
      const settingsJSON = JSON.stringify(settings, null, 2);
      const blob = new Blob([settingsJSON], {type: 'application/json'});
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = 'youtube-reading-friendly-settings.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    });
  }

  // Import settings
  function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const settings = JSON.parse(e.target.result);
          chrome.storage.sync.set(settings, function() {
            loadSettings();
            showNotification('Settings imported successfully!');
          });
        } catch (error) {
          showNotification('Error importing settings: Invalid file format', true);
        }
      };
      reader.readAsText(file);
    };

    input.click();
  }

  // Change keyboard shortcut
  function changeShortcut() {
    const oldValue = toggleShortcutInput.value;
    toggleShortcutInput.value = 'Press any key...';
    toggleShortcutInput.readOnly = false;

    const keyHandler = function(e) {
      e.preventDefault();

      // Get modifier keys
      const modifiers = [];
      if (e.ctrlKey) modifiers.push('Ctrl');
      if (e.altKey) modifiers.push('Alt');
      if (e.shiftKey) modifiers.push('Shift');

      // Get the main key
      let key = e.key;
      if (key === ' ') key = 'Space';
      if (key === 'Control' || key === 'Alt' || key === 'Shift') {
        // If only modifier keys are pressed, wait for the next key
        return;
      }

      // Format the shortcut
      const shortcut = [...modifiers, key].join('+');
      toggleShortcutInput.value = shortcut;

      // Clean up
      document.removeEventListener('keydown', keyHandler);
      toggleShortcutInput.readOnly = true;
    };

    document.addEventListener('keydown', keyHandler);

    // Cancel shortcut change if clicked elsewhere
    const cancelHandler = function(e) {
      if (e.target !== toggleShortcutInput && e.target !== changeShortcutBtn) {
        document.removeEventListener('keydown', keyHandler);
        document.removeEventListener('click', cancelHandler);
        toggleShortcutInput.readOnly = true;

        // Restore old value if no new shortcut was set
        if (toggleShortcutInput.value === 'Press any key...') {
          toggleShortcutInput.value = oldValue;
        }
      }
    };

    document.addEventListener('click', cancelHandler);
  }

  // Change focus mode keyboard shortcut
  function changeFocusShortcut() {
    const oldValue = focusModeShortcutInput.value;
    focusModeShortcutInput.value = 'Press any key...';
    focusModeShortcutInput.readOnly = false;

    const keyHandler = function(e) {
      e.preventDefault();

      // Get modifier keys
      const modifiers = [];
      if (e.ctrlKey) modifiers.push('Ctrl');
      if (e.altKey) modifiers.push('Alt');
      if (e.shiftKey) modifiers.push('Shift');

      // Get the main key
      let key = e.key;
      if (key === ' ') key = 'Space';
      if (key === 'Control' || key === 'Alt' || key === 'Shift') {
        // If only modifier keys are pressed, wait for the next key
        return;
      }

      // Format the shortcut
      const shortcut = [...modifiers, key].join('+');
      focusModeShortcutInput.value = shortcut;

      // Clean up
      document.removeEventListener('keydown', keyHandler);
      focusModeShortcutInput.readOnly = true;
    };

    document.addEventListener('keydown', keyHandler);

    // Cancel shortcut change if clicked elsewhere
    const cancelHandler = function(e) {
      if (e.target !== focusModeShortcutInput && e.target !== changeFocusShortcutBtn) {
        document.removeEventListener('keydown', keyHandler);
        document.removeEventListener('click', cancelHandler);
        focusModeShortcutInput.readOnly = true;

        // Restore old value if no new shortcut was set
        if (focusModeShortcutInput.value === 'Press any key...') {
          focusModeShortcutInput.value = oldValue;
        }
      }
    };

    document.addEventListener('click', cancelHandler);
  }

  // Show notification
  function showNotification(message, isError = false) {
    const notification = document.createElement('div');
    notification.className = `notification ${isError ? 'error' : 'success'}`;
    notification.textContent = message;

    // Style the notification
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '4px';
    notification.style.backgroundColor = isError ? '#f44336' : '#4caf50';
    notification.style.color = 'white';
    notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
    notification.style.zIndex = '9999';
    notification.style.transition = 'opacity 0.3s';

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // Function to update Ant Design slider for max width
  function updateAntSlider(value) {
    const min = 720;
    const max = 1920;
    const percentage = ((value - min) / (max - min)) * 100;

    console.log('Updating Ant slider:', { value, percentage });

    // Update track width with !important to ensure it applies
    const track = document.getElementById('max-width-track');
    if (track) {
      track.style.setProperty('width', `${percentage}%`, 'important');
      console.log('Track width set to:', `${percentage}%`);
      console.log('Track computed style:', window.getComputedStyle(track).width);
    } else {
      console.warn('Track element not found');
    }

    // Update handle position
    const handle = document.getElementById('max-width-handle');
    if (handle) {
      handle.style.setProperty('left', `${percentage}%`, 'important');
      console.log('Handle position set to:', `${percentage}%`);
    } else {
      console.warn('Handle element not found');
    }

    // Update active dots
    const dots = document.querySelectorAll('#max-width-slider .ant-slider-dot');
    const steps = [720, 960, 1200, 1440, 1680, 1920];
    console.log('Found dots:', dots.length);
    dots.forEach((dot, index) => {
      if (steps[index] <= value) {
        dot.classList.add('ant-slider-dot-active');
      } else {
        dot.classList.remove('ant-slider-dot-active');
      }
    });
  }

  // Function to update Ant Design slider for focus opacity
  function updateFocusOpacitySlider(value) {
    const min = 0;
    const max = 95;
    const percentage = ((value - min) / (max - min)) * 100;

    console.log('Updating Focus Opacity slider:', { value, percentage });

    // Update track width
    const track = document.getElementById('focus-opacity-track');
    if (track) {
      track.style.setProperty('width', `${percentage}%`, 'important');
    } else {
      console.warn('Focus opacity track element not found');
    }

    // Update handle position
    const handle = document.getElementById('focus-opacity-handle');
    if (handle) {
      handle.style.setProperty('left', `${percentage}%`, 'important');
    } else {
      console.warn('Focus opacity handle element not found');
    }

    // Update active dots
    const dots = document.querySelectorAll('#focus-opacity-slider .ant-slider-dot');
    const steps = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 95];
    dots.forEach((dot, index) => {
      if (steps[index] <= value) {
        dot.classList.add('ant-slider-dot-active');
      } else {
        dot.classList.remove('ant-slider-dot-active');
      }
    });
  }

  // Event listeners
  maxWidthRange.addEventListener('input', function() {
    const value = parseInt(this.value);
    maxWidthValue.value = `${value}px`;
    updateAntSlider(value);
  });

  // Handle custom max-width input
  maxWidthValue.addEventListener('input', function() {
    // Remove all non-numeric characters and px suffix
    let value = this.value.replace(/\D/g, '');

    if (value === '') {
      this.value = '';
      return;
    }

    // Parse the numeric value (no restrictions - allow any value)
    value = parseInt(value);

    // Update the input field with only the number (no px suffix during typing)
    this.value = value.toString();

    // Update the slider based on custom value
    if (value >= 720 && value <= 1920) {
      // Value is within slider range - move slider to exact position
      maxWidthRange.value = value;
      updateAntSlider(value);
    } else if (value > 1920) {
      // Value is higher than max - move slider to tail (maximum position)
      maxWidthRange.value = 1920;
      updateAntSlider(1920);
    } else if (value < 720) {
      // Value is lower than min - move slider to head (minimum position)
      maxWidthRange.value = 720;
      updateAntSlider(720);
    }
  });

  // Handle focus event to remove px suffix for editing
  maxWidthValue.addEventListener('focus', function() {
    let value = this.value.replace(/\D/g, '');
    if (value !== '') {
      this.value = value;
    }
  });

  // Handle blur event to add px suffix
  maxWidthValue.addEventListener('blur', function() {
    let value = this.value.replace(/\D/g, '');
    if (value === '') {
      value = '960'; // Default value
    }
    this.value = `${value}px`;
  });

  // Handle Enter key to save immediately
  maxWidthValue.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      this.blur(); // Trigger blur event
      saveSettings(); // Save immediately
    }
  });

  focusOpacityRange.addEventListener('input', function() {
    const value = parseInt(this.value);
    focusOpacityValue.value = `${value}%`;
    updateFocusOpacitySlider(value);
  });

  // Handle custom focus opacity input
  focusOpacityValue.addEventListener('input', function() {
    // Remove all non-numeric characters and % suffix
    let value = this.value.replace(/\D/g, '');

    if (value === '') {
      this.value = '';
      return;
    }

    // Parse the numeric value and clamp to valid range
    value = parseInt(value);
    value = Math.max(0, Math.min(95, value));

    // Update the input field with only the number (no % suffix during typing)
    this.value = value.toString();

    // Update the slider
    focusOpacityRange.value = value;
    updateFocusOpacitySlider(value);
  });

  // Handle focus event to remove % suffix for editing
  focusOpacityValue.addEventListener('focus', function() {
    let value = this.value.replace(/\D/g, '');
    if (value !== '') {
      this.value = value;
    }
  });

  // Handle blur event to add % suffix
  focusOpacityValue.addEventListener('blur', function() {
    let value = this.value.replace(/\D/g, '');
    if (value === '') {
      value = '70'; // Default value
    }
    value = Math.max(0, Math.min(95, parseInt(value)));
    this.value = `${value}%`;
    focusOpacityRange.value = value;
    updateFocusOpacitySlider(value);
  });

  // Handle Enter key to save immediately
  focusOpacityValue.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      this.blur(); // Trigger blur event
      saveSettings(); // Save immediately
    }
  });

  fontFamilySelect.addEventListener('change', updateUI);
  hideSidebarCheckbox.addEventListener('change', updateUI);

  changeShortcutBtn.addEventListener('click', changeShortcut);
  changeFocusShortcutBtn.addEventListener('click', changeFocusShortcut);
  exportSettingsBtn.addEventListener('click', exportSettings);
  importSettingsBtn.addEventListener('click', importSettings);
  resetAllBtn.addEventListener('click', resetAllSettings);
  saveOptionsBtn.addEventListener('click', saveSettings);

  // Initialize
  loadSettings();

  // Ensure sliders are initialized after everything is loaded
  setTimeout(() => {
    if (maxWidthRange.value) {
      updateAntSlider(parseInt(maxWidthRange.value));
    }
    if (focusOpacityRange.value) {
      updateFocusOpacitySlider(parseInt(focusOpacityRange.value));
    }
  }, 200);
});
