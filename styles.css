/* This CSS file is injected into YouTube pages via content_scripts */
/* Basic styles are defined here, but most styling is done dynamically in content.js */
/* YouTube's default font size is 10px, which we'll scale based on user settings */
html{
  font-size: 10px; /* YouTube's default, will be changed by Text Scaling setting */
}

/* #contents.style-scope.ytd-item-section-renderer.style-scope.ytd-item-section-renderer, */
#sections.ytd-comments,
#related #contents.style-scope.ytd-item-section-renderer
/* Uncomment the following lines to style the playlist card */
/* yt-lockup-view-model.ytd-grid-renderer.lockup */
/* Uncomment the following lines to style the video grid */
/* #items #dismissible, */
/* #items .yt-lockup-view-model-wiz */
{
  background-color: var(--yt-spec-badge-chip-background) !important;
  padding: 2px 10px;
  border-radius: 12px;
  margin: 8px auto 48px auto !important;
  justify-content: space-between !important;
}
/* #primary,.style-scope.ytd-two-column-browse-results-renderer{
  margin-top:8px;
} */

ytd-thumbnail[size=large] a.ytd-thumbnail, ytd-thumbnail[size=large]::before{
  border-radius: 12px 12px 0 0 !important;
}

.ytd-browse #contents.style-scope.ytd-rich-grid-renderer
,.ytd-search #contents.style-scope.ytd-section-list-renderer
,ytd-two-column-browse-results-renderer[page-subtype=history]
,ytd-item-section-renderer[page-subtype=channels] #contents.style-scope.ytd-item-section-renderer.style-scope.ytd-item-section-renderer:not(.featured-page-style)
{
  /* margin-top:24px; */
  box-sizing: border-box;
  background-color: var(--yt-spec-badge-chip-background) !important;
  border-radius: 12px;
  justify-content: center !important;
  padding : 48px 48px !important;
  margin-bottom:48px;
}

ytd-backstage-post-renderer[uses-full-lockup]{
  background-color: var(--yt-spec-badge-chip-background) !important;
}

/* ytd-item-section-renderer[page-subtype=channels] #contents.style-scope.ytd-item-section-renderer.style-scope.ytd-item-section-renderer:not(.featured-page-style)
{
  padding: 48px 12px 48px 12px !important;
} */

.featured-page-style{
  margin-top:48px !important;
  margin-bottom:0px !important;
  padding : 0 24px !important;
  box-sizing: border-box;
  background-color: var(--yt-spec-badge-chip-background) !important;
  border-radius: 12px;
  justify-content: center !important;
}

/* #header{
  padding:0 24px !important;
} */

ytd-browse[page-subtype=channels] #header.style-scope.ytd-rich-grid-renderer,ytd-search #header.style-scope.ytd-search{
  margin-bottom: 24px;
  padding:0 24px !important;
}
/* padding:calc(var(--ytd-rich-grid-shorts-item-margin)/2) !important; */

/* .shortsLockupViewModelHostOutsideMetadata{
  padding-left: 8px !important;
} */

#items{
  justify-content: center !important;
}

.yt-spec-button-shape-next--mono.yt-spec-button-shape-next--outline{
  background-color: var(--yt-spec-base-background) !important;
}

ytd-rich-item-renderer[no-gutter-margins]:first-of-type:not([is-shorts-grid])
,ytd-rich-item-renderer[no-gutter-margins][rendered-from-rich-grid][is-in-first-column]:not([is-shorts-grid])
{
  margin-left:calc(var(--ytd-rich-grid-item-margin)/2 + var(--ytd-rich-grid-gutter-margin)) !important;
}

ytd-rich-grid-renderer[is-shorts-grid] #contents{
  padding-top: 0 !important;
}

.button-container.ytd-rich-shelf-renderer{
  background-color: transparent !important;
}

ytd-section-list-renderer:not([hide-bottom-separator]):not([page-subtype=history]):not([page-subtype=memberships-and-purchases]):not([page-subtype=ypc-offers]):not([live-chat-engagement-panel]) #contents.ytd-section-list-renderer>*.ytd-section-list-renderer:not(:last-child):not(ytd-page-introduction-renderer):not([item-dismissed]):not([has-destination-shelf-renderer]):not(ytd-minor-moment-header-renderer):not([has-section-group-view-model]){
  border-bottom: none !important;
}

ytd-backstage-items.style-scope.ytd-section-list-renderer,ytd-section-list-renderer[page-subtype=channels] ytd-comments.ytd-section-list-renderer{
  margin: 8px auto !important;
}

/* ytd-grid-video-renderer,ytd-thumbnail.ytd-grid-video-renderer{
  width: calc(var(--ytd-grid-video-width,210px) + 20px) !important;
} */

/* #description.ytd-watch-metadata{
  min-width: 100% !important;
} */

ytd-tabbed-page-header.grid-6-columns tp-yt-paper-tabs.ytd-tabbed-page-header{
  margin: 0 !important;
}

.html5-video-container{
  max-width: 100% !important;
  max-height: 100% !important;
}
ytd-watch-flexy[flexy] #player-container-outer.ytd-watch-flexy{
  max-width: 100% !important;
  max-height: 100% !important;
}
ytd-watch-flexy[fixed-panels] #primary.ytd-watch-flexy{
  min-width: auto !important;
}
ytd-app[mini-guide-visible] ytd-mini-guide-renderer{
  z-index: 2028 !important;
}